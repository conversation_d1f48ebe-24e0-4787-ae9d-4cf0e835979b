#!/usr/bin/env python3
"""
Zeitwahl AI Agent - Main Application Entry Point

This module orchestrates the entire Actor-based system, initializing the ActorSystem
and managing the lifecycle of all actors in the application.
"""

import asyncio
import sys

from app.config import settings
from app.utils.logging.logging_config import setup_enhanced_logging, get_logger

# Actor imports
from app.infrastructure.database.db_actor import DBActor
from app.infrastructure.telegram.bot_actor import BotActor


class ZeitwählApp:
    """Main application class that orchestrates the Actor-based system."""

    def __init__(self):
        self.actors = {}
        self.running = False

        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Configure enhanced logging for the application."""
        # Set up enhanced logging with colors and emojis
        setup_enhanced_logging(
            log_level=settings.app.log_level,
            use_colors=True,
            show_emojis=True,
            log_to_file=settings.app.environment == "production",
            log_file_path="zeitwahl.log" if settings.app.environment == "production" else None
        )

        logger = get_logger(__name__)
        logger.info(f"🚀 Enhanced logging configured at {settings.app.log_level} level")

    async def initialize(self):
        """Initialize the Actor-based system."""
        logger = get_logger(__name__)
        logger.info("Initializing Zeitwahl AI Agent with Actor-based architecture...")

        try:
            # Validate configuration
            await self._validate_configuration()

            logger.info("🔧 Setting up actors for testing...")

            # Create actors for testing (excluding LLM and preprocessing actors)
            self.actors = {
                "db_actor": DBActor(),
                "bot_actor": BotActor()
            }

            logger.info("✅ All actors created with dependency injection")

        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            raise
    
    async def _validate_configuration(self):
        """Validate critical configuration settings."""
        logger = get_logger(__name__)

        # Check required settings
        if not settings.telegram.bot_token:
            raise ValueError("TELEGRAM_BOT_TOKEN is required")

        # Warn about missing optional settings
        if not settings.llm.gemini_api_key and not settings.llm.deepseek_api_key:
            logger.warning("No LLM API keys configured, using mock provider only")

        if not settings.calendar.google_client_id and not settings.calendar.outlook_client_id:
            logger.warning("No calendar integrations configured, using mock provider only")

        logger.info("Configuration validation completed")

    async def start(self):
        """Start the Actor-based application."""
        logger = get_logger(__name__)

        if self.running:
            logger.warning("Application is already running")
            return

        try:
            logger.info("Starting Zeitwahl AI Agent with actors...")
            self.running = True

            # Deploy actors individually to handle each one's failure independently
            # Start with database actor
            try:
                logger.info("🚀 Deploying db_actor...")
                await self.actors["db_actor"].deploy()
                logger.info("✅ db_actor deployed successfully")
            except Exception as e:
                logger.error(f"💥 Failed to deploy db_actor: {e}")
                raise

            # Start bot actor
            try:
                logger.info("🚀 Deploying bot_actor...")
                await self.actors["bot_actor"].deploy()
                logger.info("✅ bot_actor deployed successfully")
            except Exception as e:
                logger.error(f"💥 Failed to deploy bot_actor: {e}")
                raise

            logger.info("🎉 All actors deployed successfully")

            # Keep the application running
            import signal
            import asyncio

            # Set up signal handlers for graceful shutdown
            def signal_handler(signum, _frame):
                logger.info(f"Received signal {signum}, shutting down...")
                asyncio.create_task(self.shutdown())

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # Wait indefinitely
            while self.running:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            await self.shutdown()
            raise

    async def shutdown(self):
        """Gracefully shutdown the Actor-based application."""
        logger = get_logger(__name__)

        if not self.running:
            return

        logger.info("Shutting down Zeitwahl AI Agent...")
        self.running = False

        try:
            # Stop actors in reverse order
            shutdown_order = ["bot_actor", "db_actor"]

            for actor_name in shutdown_order:
                if actor_name in self.actors:
                    actor = self.actors[actor_name]
                    logger.info(f"Stopping {actor_name}...")
                    try:
                        await actor.stop()
                        logger.info(f"{actor_name} stopped successfully")
                    except Exception as e:
                        logger.error(f"Error stopping {actor_name}: {e}")

            logger.info("Shutdown completed successfully")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def health_check(self) -> dict:
        """Perform application health check."""
        health_status = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "components": {},
            "actors": {}
        }

        try:
            # Check individual actors
            for name, actor in self.actors.items():
                if actor.is_running():
                    health_status["actors"][name] = "running"
                elif actor.has_error():
                    health_status["actors"][name] = "error"
                    health_status["status"] = "degraded"
                else:
                    health_status["actors"][name] = "stopped"
                    health_status["status"] = "degraded"

            # Overall system status
            running_actors = sum(1 for actor in self.actors.values() if actor.is_running())
            total_actors = len(self.actors)

            health_status["components"]["total_actors"] = total_actors
            health_status["components"]["running_actors"] = running_actors
            health_status["components"]["application_running"] = self.running

            if running_actors == 0:
                health_status["status"] = "stopped"
            elif running_actors < total_actors:
                health_status["status"] = "degraded"

        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)

        return health_status


async def main():
    """Main entry point for the application."""
    logger = get_logger(__name__)
    
    try:
        # Create and initialize the application
        app = ZeitwählApp()
        await app.initialize()
        
        # Start the application
        logger.info("🚀 Zeitwahl AI Agent is starting...")
        logger.info(f"Environment: {settings.app.environment}")
        logger.info(f"Debug mode: {settings.app.debug}")
        
        await app.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nGracefully shutting down...")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
